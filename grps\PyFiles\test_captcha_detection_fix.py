#!/usr/bin/env python3
"""
Test script for enhanced image CAPTCHA detection fix
Validates that false positive detection has been resolved
"""

import sys
import os
import logging
from unittest.mock import Mock, MagicMock

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def setup_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def create_mock_element(is_displayed=True, has_src=True, src_value="https://example.com/captcha.png", 
                       width=100, height=50, is_enabled=True, style=""):
    """Create a mock WebElement for testing"""
    mock_element = Mock()
    mock_element.is_displayed.return_value = is_displayed
    mock_element.is_enabled.return_value = is_enabled
    mock_element.size = {'width': width, 'height': height}
    mock_element.get_attribute.return_value = src_value if has_src else None
    
    # Handle different attribute requests
    def get_attribute_side_effect(attr):
        if attr == 'src':
            return src_value if has_src else None
        elif attr == 'style':
            return style
        return None
    
    mock_element.get_attribute.side_effect = get_attribute_side_effect
    return mock_element

def test_validate_captcha_element():
    """Test the _validate_captcha_element method"""
    logger = setup_logging()
    logger.info("=== Testing Enhanced CAPTCHA Element Validation ===")
    
    try:
        # Import the Groups class
        from groups import Groups
        
        # Create a Groups instance with mocked browser
        groups = Groups()
        groups.logger = logger
        
        # Test cases for validation
        test_cases = [
            {
                'name': 'Valid CAPTCHA image',
                'element': create_mock_element(
                    is_displayed=True, 
                    has_src=True, 
                    src_value="https://accounts.google.com/captcha.png",
                    width=150, 
                    height=75
                ),
                'xpath': "//img[@id='captchaimg']",
                'expected': True
            },
            {
                'name': 'Hidden CAPTCHA element',
                'element': create_mock_element(
                    is_displayed=False,
                    has_src=True,
                    src_value="https://accounts.google.com/captcha.png"
                ),
                'xpath': "//img[@id='captchaimg']",
                'expected': False
            },
            {
                'name': 'CAPTCHA image without src',
                'element': create_mock_element(
                    is_displayed=True,
                    has_src=False,
                    src_value=None
                ),
                'xpath': "//img[@id='captchaimg']",
                'expected': False
            },
            {
                'name': 'CAPTCHA image with empty src',
                'element': create_mock_element(
                    is_displayed=True,
                    has_src=True,
                    src_value=""
                ),
                'xpath': "//img[@id='captchaimg']",
                'expected': False
            },
            {
                'name': 'CAPTCHA image with placeholder src',
                'element': create_mock_element(
                    is_displayed=True,
                    has_src=True,
                    src_value="about:blank"
                ),
                'xpath': "//img[@id='captchaimg']",
                'expected': False
            },
            {
                'name': 'CAPTCHA image with zero dimensions',
                'element': create_mock_element(
                    is_displayed=True,
                    has_src=True,
                    src_value="https://accounts.google.com/captcha.png",
                    width=0,
                    height=0
                ),
                'xpath': "//img[@id='captchaimg']",
                'expected': False
            },
            {
                'name': 'CAPTCHA input field (valid)',
                'element': create_mock_element(
                    is_displayed=True,
                    has_src=False,  # Input fields don't have src
                    width=200,
                    height=30,
                    is_enabled=True
                ),
                'xpath': "//input[@name='ca']",
                'expected': True
            },
            {
                'name': 'CAPTCHA input field (disabled)',
                'element': create_mock_element(
                    is_displayed=True,
                    has_src=False,
                    width=200,
                    height=30,
                    is_enabled=False
                ),
                'xpath': "//input[@name='ca']",
                'expected': False
            },
            {
                'name': 'CAPTCHA element hidden via CSS',
                'element': create_mock_element(
                    is_displayed=True,
                    has_src=True,
                    src_value="https://accounts.google.com/captcha.png",
                    style="display: none;"
                ),
                'xpath': "//img[@id='captchaimg']",
                'expected': False
            }
        ]
        
        passed = 0
        failed = 0
        
        for test_case in test_cases:
            try:
                result = groups._validate_captcha_element(test_case['element'], test_case['xpath'])
                if result == test_case['expected']:
                    logger.info(f"✅ {test_case['name']}: PASSED (Expected: {test_case['expected']}, Got: {result})")
                    passed += 1
                else:
                    logger.error(f"❌ {test_case['name']}: FAILED (Expected: {test_case['expected']}, Got: {result})")
                    failed += 1
            except Exception as e:
                logger.error(f"❌ {test_case['name']}: ERROR - {str(e)}")
                failed += 1
        
        logger.info(f"\n--- Test Results ---")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        logger.info(f"Total: {passed + failed}")
        
        if failed == 0:
            logger.info("🎉 All validation tests passed!")
            return True
        else:
            logger.error(f"⚠️ {failed} test(s) failed.")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in test setup: {str(e)}")
        return False

def test_method_existence():
    """Test that all required methods exist"""
    logger = setup_logging()
    logger.info("=== Testing Method Existence ===")
    
    try:
        from groups import Groups
        
        required_methods = [
            '_validate_captcha_element',
            '_is_captcha_element_present_but_invalid',
            'ImageCaptchaVerif',
            'extract_image_captcha',
            'ImageCaptchaSolver'
        ]
        
        for method_name in required_methods:
            if hasattr(Groups, method_name):
                logger.info(f"✅ {method_name} method exists")
            else:
                logger.error(f"❌ {method_name} method missing")
                return False
        
        logger.info("✅ All required methods exist")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking methods: {str(e)}")
        return False

def main():
    """Main test function"""
    logger = setup_logging()
    logger.info("🚀 Starting Enhanced CAPTCHA Detection Fix Tests")
    
    tests = [
        ("Method Existence", test_method_existence),
        ("CAPTCHA Element Validation", test_validate_captcha_element)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            if test_func():
                logger.info(f"✅ {test_name} test PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} test FAILED")
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} test FAILED with exception: {e}")
            failed += 1
    
    logger.info(f"\n=== Final Test Results ===")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Total: {passed + failed}")
    
    if failed == 0:
        logger.info("🎉 All tests passed! Enhanced CAPTCHA detection fix is working correctly.")
        return True
    else:
        logger.error(f"⚠️ {failed} test(s) failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
