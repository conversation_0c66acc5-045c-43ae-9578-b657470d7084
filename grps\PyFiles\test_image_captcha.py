#!/usr/bin/env python3
"""
Test script for image CAPTCHA functionality
This script tests the image CAPTCHA detection and solving capabilities
"""

import sys
import os
import logging
from time import sleep

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import the main groups module
try:
    from groups import Groups
    print("✅ Successfully imported Groups class")
except ImportError as e:
    print(f"❌ Failed to import Groups class: {e}")
    sys.exit(1)

# Test anti-captcha library availability
try:
    from python_anticaptcha import AnticaptchaClient, ImageToTextTask
    print("✅ Anti-captcha library is available")
    ANTICAPTCHA_AVAILABLE = True
except ImportError as e:
    print(f"❌ Anti-captcha library not available: {e}")
    ANTICAPTCHA_AVAILABLE = False

def setup_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_image_captcha.log')
        ]
    )
    return logging.getLogger(__name__)

def test_image_captcha_detection():
    """Test image CAPTCHA detection methods"""
    logger = setup_logging()
    logger.info("=== Testing Image CAPTCHA Detection ===")

    try:
        # Import the Groups class without instantiating
        from groups import Groups
        
        # Test XPath definitions
        from groups import image_captcha_xpaths
        logger.info(f"Image CAPTCHA XPaths defined: {len(image_captcha_xpaths)}")
        for xpath in image_captcha_xpaths:
            logger.info(f"  - {xpath}")
        
        # Test method availability by checking if they exist in the class
        methods_to_check = [
            'ImageCaptchaVerif',
            'extract_image_captcha',
            'solve_image_captcha',
            'submit_image_captcha',
            'ImageCaptchaSolver'
        ]

        for method_name in methods_to_check:
            if hasattr(Groups, method_name):
                logger.info(f"✅ {method_name} method is available")
            else:
                logger.error(f"❌ {method_name} method not found")
                return False
        
        logger.info("✅ All image CAPTCHA methods are properly defined")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing image CAPTCHA detection: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    logger = setup_logging()
    logger.info("=== Testing Configuration ===")

    try:
        from groups import Groups

        # Test configuration method
        if hasattr(Groups, 'get_anticaptcha_config'):
            logger.info("✅ get_anticaptcha_config method is available")
        else:
            logger.error("❌ get_anticaptcha_config method not found")
            return False

        # Test validation method
        if hasattr(Groups, 'validate_captcha_solution'):
            logger.info("✅ validate_captcha_solution method is available")
        else:
            logger.error("❌ validate_captcha_solution method not found")
            return False
        
        logger.info("✅ Configuration testing completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing configuration: {e}")
        return False

def test_integration():
    """Test integration with existing login flow"""
    logger = setup_logging()
    logger.info("=== Testing Integration ===")

    try:
        from groups import Groups

        # Check if login method has been modified
        import inspect
        login_source = inspect.getsource(Groups.login)
        
        if 'ImageCaptchaVerif' in login_source:
            logger.info("✅ Login method includes image CAPTCHA detection")
        else:
            logger.error("❌ Login method does not include image CAPTCHA detection")
            return False
            
        if 'ImageCaptchaSolver' in login_source:
            logger.info("✅ Login method includes image CAPTCHA solving")
        else:
            logger.error("❌ Login method does not include image CAPTCHA solving")
            return False
        
        logger.info("✅ Integration testing completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing integration: {e}")
        return False

def main():
    """Main test function"""
    logger = setup_logging()
    logger.info("🚀 Starting Image CAPTCHA Implementation Tests")
    
    tests = [
        ("Image CAPTCHA Detection", test_image_captcha_detection),
        ("Configuration", test_configuration),
        ("Integration", test_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            if test_func():
                logger.info(f"✅ {test_name} test PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} test FAILED")
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} test FAILED with exception: {e}")
            failed += 1
    
    logger.info(f"\n=== Test Results ===")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Total: {passed + failed}")
    
    if failed == 0:
        logger.info("🎉 All tests passed! Image CAPTCHA implementation is ready.")
        return True
    else:
        logger.error(f"⚠️ {failed} test(s) failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
